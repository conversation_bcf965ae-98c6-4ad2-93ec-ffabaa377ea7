@import url(http://fonts.googleapis.com/css?family=Raleway:200,500,700,800);

@font-face {
	font-weight: normal;
	font-style: normal;
	font-family: 'codropsicons';
	src:url('../fonts/codropsicons/codropsicons.eot');
	src:url('../fonts/codropsicons/codropsicons.eot?#iefix') format('embedded-opentype'),
		url('../fonts/codropsicons/codropsicons.woff') format('woff'),
		url('../fonts/codropsicons/codropsicons.ttf') format('truetype'),
		url('../fonts/codropsicons/codropsicons.svg#codropsicons') format('svg');
}

*, *:after, *:before { -webkit-box-sizing: border-box; box-sizing: border-box; }
.clearfix:before, .clearfix:after { content: ''; display: table; }
.clearfix:after { clear: both; }

body {
	background: #f9f7f6;
	color: #404d5b;
	font-weight: 500;
	font-size: 1.05em;
	font-family: 'Raleway', Arial, sans-serif;
}

a {
	color: #2fa0ec;
	text-decoration: none;
	outline: none;
}

a:hover, a:focus {
	color: #404d5b;
}

.container {
	margin: 0 auto;
	text-align: center;
	overflow: hidden;
}

.content {
	font-size: 150%;
	padding: 3em 0;
}

.content h2 {
	margin: 0 0 2em;
	opacity: 0.1;
}

.content p {
	margin: 1em 0;
	padding: 5em 0 0 0;
	font-size: 0.65em;
}

.bgcolor-1 { background: #f0efee; }
.bgcolor-2 { background: #f9f9f9; }
.bgcolor-3 { background: #e8e8e8; }
.bgcolor-4 { background: #2f3238; color: #fff; }
.bgcolor-5 { background: #df6659; color: #521e18; }
.bgcolor-6 { background: #2fa8ec; color: #fff;}
.bgcolor-7 { background: #d0d6d6; }
.bgcolor-8 { background: #3d4444; color: #fff; }
.bgcolor-9 { background: #8781bd; color: #fff; }

body .nomargin-bottom {
	margin-bottom: 0;
}

/* Header */
.codrops-header {
	padding: 3em 190px 4em;
	letter-spacing: -1px;
}

.codrops-header h1 {
	font-weight: 800;
	font-size: 4em;
	line-height: 1;
	margin: 0.25em 0 0;
}

.codrops-header h1 span {
	display: block;
	font-size: 50%;
	font-weight: 400;
	padding: 0.325em 0 1em 0;
	color: #c3c8cd;
}

/* Demos nav */
.codrops-demos a {
	text-transform: uppercase;
	letter-spacing: 1px;
	font-weight: bold;
	font-size: 0.85em;
	display: inline-block;
	margin: 0 1em;
	font-family: "Avenir", "Helvetica Neue", Helvetica, Arial, sans-serif;
}

.codrops-demos a.current-demo {
	border-bottom: 2px solid;
	color: #404d5b;
}

/* Top Navigation Style */
.codrops-links {
	position: relative;
	display: inline-block;
	white-space: nowrap;
	font-size: 1.25em;
	text-align: center;
}

.codrops-links::after {
	position: absolute;
	top: 0;
	left: 50%;
	margin-left: -1px;
	width: 2px;
	height: 100%;
	background: #dbdbdb;
	content: '';
	-webkit-transform: rotate3d(0,0,1,22.5deg);
	transform: rotate3d(0,0,1,22.5deg);
}

.codrops-icon {
	display: inline-block;
	margin: 0.5em;
	padding: 0em 0;
	width: 1.5em;
	text-decoration: none;
}

.codrops-icon span {
	display: none;
}

.codrops-icon:before {
	margin: 0 5px;
	text-transform: none;
	font-weight: normal;
	font-style: normal;
	font-variant: normal;
	font-family: 'codropsicons';
	line-height: 1;
	speak: none;
	-webkit-font-smoothing: antialiased;
}

.codrops-icon--drop:before {
	content: "\e001";
}

.codrops-icon--prev:before {
	content: "\e004";
}

/* Related demos */
.content--related {
	text-align: center;
	color: #D8DADB;
	font-weight: bold;
}

.media-item {
	display: inline-block;
	padding: 1em;
	vertical-align: top;
	-webkit-transition: color 0.3s;
	transition: color 0.3s;
}

.media-item__img {
	opacity: 0.8;
	-webkit-transition: opacity 0.3s;
	transition: opacity 0.3s;
}

.media-item:hover .media-item__img,
.media-item:focus .media-item__img {
	opacity: 1;
}

.media-item__title {
	font-size: 0.75em;
	margin: 0;
	padding: 0.5em;
}

@media screen and (max-width: 50em) {
	.codrops-header {
		padding: 3em 10% 4em;
	}
}

@media screen and (max-width: 40em) {
	.codrops-header h1 {
		font-size: 2.8em;
	}
}
