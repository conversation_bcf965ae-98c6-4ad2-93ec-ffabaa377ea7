import java.io.File;

import cn.hutool.crypto.SecureUtil;
import net.mingsoft.basic.util.BasicUtil;
import net.mingsoft.config.MSProperties;

/**
 * @Title Test
 * <AUTHOR>
 * @Description //TODO
 * @Date 2025/6/10 16:57
 **/
public class Test {
    public static void main(String[] args) {
//        MSProperties.upload.template = "template";
//        System.out.println(BasicUtil.getRealTemplatePath(MSProperties.upload.template  + File.separator + "1" + File.separator));
        System.out.println(SecureUtil.md5("xtz@20250610"));
    }
}
